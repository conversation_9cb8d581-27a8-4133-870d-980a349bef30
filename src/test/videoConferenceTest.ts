/**
 * 视频会议组件测试文件
 * 这个文件包含了测试视频会议功能的基本测试用例
 */

import { useVideoConferenceStore } from '@/stores/videoConference';
import { generateRoomId, validateRoomId, formatRoomId } from '@/utils/roomUtils';

// 测试房间工具函数
export function testRoomUtils() {
  console.log('=== 测试房间工具函数 ===');
  
  // 测试生成房间ID
  const roomId = generateRoomId();
  console.log('生成的房间ID:', roomId);
  
  // 测试验证房间ID
  const isValid = validateRoomId(roomId);
  console.log('房间ID是否有效:', isValid);
  
  // 测试格式化房间ID
  const formatted = formatRoomId('123456789');
  console.log('格式化的房间ID:', formatted);
  
  console.log('房间工具函数测试完成\n');
}

// 测试视频会议Store
export function testVideoConferenceStore() {
  console.log('=== 测试视频会议Store ===');
  
  const store = useVideoConferenceStore();
  
  // 测试初始状态
  console.log('初始状态:', {
    status: store.status,
    isConnected: store.isConnected,
    isConnecting: store.isConnecting,
    hasError: store.hasError,
  });
  
  // 测试设备获取
  store.fetchAvailableDevices().then(() => {
    console.log('可用设备:', {
      cameras: store.availableDevices.cameras.length,
      microphones: store.availableDevices.microphones.length,
      speakers: store.availableDevices.speakers.length,
    });
  });
  
  console.log('视频会议Store测试完成\n');
}

// 测试NERTC SDK集成
export async function testNERTCIntegration() {
  console.log('=== 测试NERTC SDK集成 ===');
  
  const store = useVideoConferenceStore();
  
  try {
    // 测试客户端初始化
    console.log('正在初始化客户端...');
    const initSuccess = await store.initializeClient();
    console.log('客户端初始化结果:', initSuccess);
    
    if (initSuccess) {
      console.log('NERTC SDK集成测试成功');
    } else {
      console.log('NERTC SDK集成测试失败');
    }
  } catch (error) {
    console.error('NERTC SDK集成测试出错:', error);
  }
  
  console.log('NERTC SDK集成测试完成\n');
}

// 模拟会议流程测试
export async function testMeetingFlow() {
  console.log('=== 测试会议流程 ===');
  
  const store = useVideoConferenceStore();
  const testRoomId = generateRoomId();
  const testUserName = 'TestUser';
  
  try {
    // 1. 初始化客户端
    console.log('步骤1: 初始化客户端');
    await store.initializeClient();
    
    // 2. 创建本地流
    console.log('步骤2: 创建本地流');
    await store.createLocalStream();
    
    // 3. 模拟加入房间（不实际连接）
    console.log('步骤3: 模拟加入房间');
    console.log(`房间ID: ${testRoomId}, 用户名: ${testUserName}`);
    
    // 4. 测试音视频控制
    console.log('步骤4: 测试音视频控制');
    console.log('初始音频状态:', store.isAudioEnabled);
    console.log('初始视频状态:', store.isVideoEnabled);
    
    // 5. 模拟离开房间
    console.log('步骤5: 模拟离开房间');
    
    console.log('会议流程测试完成');
  } catch (error) {
    console.error('会议流程测试出错:', error);
  }
  
  console.log('会议流程测试完成\n');
}

// 运行所有测试
export async function runAllTests() {
  console.log('开始运行视频会议组件测试...\n');
  
  testRoomUtils();
  testVideoConferenceStore();
  await testNERTCIntegration();
  await testMeetingFlow();
  
  console.log('所有测试完成！');
}

// 如果在浏览器环境中，可以通过控制台运行测试
if (typeof window !== 'undefined') {
  (window as any).videoConferenceTest = {
    testRoomUtils,
    testVideoConferenceStore,
    testNERTCIntegration,
    testMeetingFlow,
    runAllTests,
  };
  
  console.log('视频会议测试函数已挂载到 window.videoConferenceTest');
  console.log('可以在控制台中运行: window.videoConferenceTest.runAllTests()');
}
