<template>
  <div class="participant-list">
    <a-drawer
      v-model:visible="visible"
      title="Participants"
      placement="right"
      :width="320"
    >
      <div class="participants-content">
        <!-- Local User -->
        <div class="participant-item local-participant">
          <div class="participant-avatar">
            <a-avatar :size="40">
              {{ getInitials(displayName) }}
            </a-avatar>
          </div>
          <div class="participant-info">
            <div class="participant-name">
              {{ displayName }} (You)
            </div>
            <div class="participant-status">
              <a-tag v-if="!isAudioEnabled" color="red" size="small">
                <template #icon>
                  <icon-mute />
                </template>
                Muted
              </a-tag>
              <a-tag v-if="!isVideoEnabled" color="orange" size="small">
                <template #icon>
                  <icon-camera-off />
                </template>
                Camera Off
              </a-tag>
              <a-tag v-if="isScreenSharing" color="blue" size="small">
                <template #icon>
                  <icon-desktop />
                </template>
                Sharing
              </a-tag>
            </div>
          </div>
          <div class="participant-actions">
            <a-dropdown>
              <a-button type="text" size="small">
                <template #icon>
                  <icon-more />
                </template>
              </a-button>
              <template #content>
                <a-doption @click="openDeviceSettings">
                  <template #icon>
                    <icon-settings />
                  </template>
                  Device Settings
                </a-doption>
              </template>
            </a-dropdown>
          </div>
        </div>

        <!-- Remote Participants -->
        <div
          v-for="stream in remoteStreamsList"
          :key="stream.getId()"
          class="participant-item"
        >
          <div class="participant-avatar">
            <a-avatar :size="40">
              {{ getInitials(stream.getId()) }}
            </a-avatar>
          </div>
          <div class="participant-info">
            <div class="participant-name">
              {{ stream.getId() }}
            </div>
            <div class="participant-status">
              <a-tag v-if="!stream.hasAudio?.()" color="red" size="small">
                <template #icon>
                  <icon-mute />
                </template>
                Muted
              </a-tag>
              <a-tag v-if="!stream.hasVideo?.()" color="orange" size="small">
                <template #icon>
                  <icon-camera-off />
                </template>
                Camera Off
              </a-tag>
            </div>
          </div>
          <div class="participant-actions">
            <a-dropdown>
              <a-button type="text" size="small">
                <template #icon>
                  <icon-more />
                </template>
              </a-button>
              <template #content>
                <a-doption @click="pinParticipant(stream.getId())">
                  <template #icon>
                    <icon-pin />
                  </template>
                  Pin Video
                </a-doption>
                <a-doption @click="muteParticipant(stream.getId())">
                  <template #icon>
                    <icon-mute />
                  </template>
                  Mute (Host only)
                </a-doption>
              </template>
            </a-dropdown>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="remoteStreamsList.length === 0" class="empty-state">
          <icon-user-group :size="48" style="color: #ccc" />
          <p>No other participants yet</p>
          <p class="empty-subtitle">
            Share the room ID to invite others
          </p>
        </div>
      </div>

      <!-- Footer Actions -->
      <template #footer>
        <div class="participant-footer">
          <a-button @click="copyRoomId" block>
            <template #icon>
              <icon-copy />
            </template>
            Copy Room ID
          </a-button>
          <a-button @click="inviteParticipants" type="primary" block>
            <template #icon>
              <icon-plus />
            </template>
            Invite Participants
          </a-button>
        </div>
      </template>
    </a-drawer>

    <!-- Device Settings Modal -->
    <DeviceSettings
      v-model:visible="showDeviceSettings"
      @save="handleDeviceSettingsSave"
    />

    <!-- Invite Modal -->
    <a-modal
      v-model:visible="showInviteModal"
      title="Invite Participants"
      :footer="false"
    >
      <div class="invite-content">
        <div class="invite-section">
          <h4>Room ID</h4>
          <a-input-group compact>
            <a-input
              :value="roomId"
              readonly
              style="width: calc(100% - 80px)"
            />
            <a-button @click="copyRoomId" style="width: 80px">
              Copy
            </a-button>
          </a-input-group>
        </div>

        <div class="invite-section">
          <h4>Share Link</h4>
          <a-input-group compact>
            <a-input
              :value="shareLink"
              readonly
              style="width: calc(100% - 80px)"
            />
            <a-button @click="copyShareLink" style="width: 80px">
              Copy
            </a-button>
          </a-input-group>
        </div>

        <div class="invite-section">
          <h4>QR Code</h4>
          <div class="qr-code-container">
            <div id="qr-code" class="qr-code"></div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useVideoConferenceStore } from '@/stores/videoConference';
import { storeToRefs } from 'pinia';
import DeviceSettings from './DeviceSettings.vue';
import {
  IconMute,
  IconCameraOff,
  IconDesktop,
  IconMore,
  IconSettings,
  IconPin,
  IconUserGroup,
  IconCopy,
  IconPlus,
} from '@arco-design/web-vue/es/icon';
import { Message } from '@arco-design/web-vue';

// Props
interface Props {
  visible: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// Store
const videoStore = useVideoConferenceStore();
const {
  displayName,
  roomId,
  remoteStreamsList,
  isAudioEnabled,
  isVideoEnabled,
  isScreenSharing,
} = storeToRefs(videoStore);

// Local state
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const showDeviceSettings = ref(false);
const showInviteModal = ref(false);

// Computed
const shareLink = computed(() => {
  return `${window.location.origin}?room=${roomId.value}`;
});

// Methods
function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

function openDeviceSettings() {
  showDeviceSettings.value = true;
}

function handleDeviceSettingsSave(settings: any) {
  console.log('Device settings saved:', settings);
  Message.success('Device settings updated');
}

function pinParticipant(participantId: string) {
  console.log('Pin participant:', participantId);
  Message.info(`Pinned ${participantId}`);
}

function muteParticipant(participantId: string) {
  console.log('Mute participant:', participantId);
  Message.info(`Muted ${participantId} (Host feature)`);
}

async function copyRoomId() {
  try {
    await navigator.clipboard.writeText(roomId.value);
    Message.success('Room ID copied to clipboard');
  } catch (error) {
    console.error('Failed to copy room ID:', error);
    Message.error('Failed to copy room ID');
  }
}

async function copyShareLink() {
  try {
    await navigator.clipboard.writeText(shareLink.value);
    Message.success('Share link copied to clipboard');
  } catch (error) {
    console.error('Failed to copy share link:', error);
    Message.error('Failed to copy share link');
  }
}

function inviteParticipants() {
  showInviteModal.value = true;
}
</script>

<style lang="scss" scoped>
.participant-list {
  .participants-content {
    padding: 0;
  }

  .participant-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &.local-participant {
      background: #f8f9fa;
      margin: -12px -24px 12px -24px;
      padding: 12px 24px;
      border-bottom: 2px solid #e8e8e8;
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .participant-avatar {
    margin-right: 12px;
  }

  .participant-info {
    flex: 1;
    min-width: 0;
  }

  .participant-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .participant-status {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
  }

  .participant-actions {
    margin-left: 8px;
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;

    p {
      margin: 8px 0;
    }

    .empty-subtitle {
      font-size: 12px;
      color: #ccc;
    }
  }

  .participant-footer {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .invite-content {
    .invite-section {
      margin-bottom: 24px;

      h4 {
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
      }
    }

    .qr-code-container {
      display: flex;
      justify-content: center;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 6px;
    }

    .qr-code {
      width: 200px;
      height: 200px;
      background: white;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #999;
      font-size: 14px;
    }
  }
}
</style>
