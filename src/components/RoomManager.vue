<template>
  <div class="room-manager">
    <a-modal
      v-model:visible="visible"
      title="创建会议"
      :width="500"
      :footer="false"
      :mask-closable="false"
      :closable="canClose"
    >
      <div class="room-manager-content">
        <a-tabs default-active-key="join">
          <a-tab-pane key="join" title="加入会议">
            <a-form :model="joinForm" layout="vertical">
              <a-form-item label="您的姓名" required>
                <a-input
                  v-model="joinForm.displayName"
                  placeholder="请输入您的姓名"
                  :disabled="isLoading"
                />
              </a-form-item>
              <a-form-item label="会议号" required>
                <a-input
                  v-model="joinForm.roomId"
                  placeholder="请输入会议号"
                  :disabled="isLoading"
                />
              </a-form-item>
              <a-form-item>
                <a-button
                  type="primary"
                  :loading="isLoading"
                  :disabled="!joinForm.displayName || !joinForm.roomId"
                  @click="handleJoinRoom"
                  long
                >
                  加入会议
                </a-button>
              </a-form-item>
            </a-form>
          </a-tab-pane>
          <a-tab-pane key="create" title="创建会议">
            <a-form :model="createForm" layout="vertical">
              <a-form-item label="您的姓名" required>
                <a-input
                  v-model="createForm.displayName"
                  placeholder="请输入您的姓名"
                  :disabled="isLoading"
                />
              </a-form-item>
              <a-form-item label="会议主题">
                <a-input
                  v-model="createForm.topic"
                  placeholder="请输入会议主题（选填）"
                  :disabled="isLoading"
                />
              </a-form-item>
              <a-form-item label="会议设置">
                <a-space direction="vertical" style="width: 100%">
                  <a-checkbox
                    v-model="createForm.settings.waitingRoom"
                    :disabled="isLoading"
                  >
                    启用等候室
                  </a-checkbox>
                  <a-checkbox
                    v-model="createForm.settings.muteOnEntry"
                    :disabled="isLoading"
                  >
                    参与者加入时静音
                  </a-checkbox>
                  <a-checkbox
                    v-model="createForm.settings.requirePassword"
                    :disabled="isLoading"
                  >
                    设置会议密码
                  </a-checkbox>
                </a-space>
              </a-form-item>
              <a-form-item
                v-if="createForm.settings.requirePassword"
                label="会议密码"
                required
              >
                <a-input-password
                  v-model="createForm.password"
                  placeholder="请设置会议密码"
                  :disabled="isLoading"
                />
              </a-form-item>
              <a-form-item>
                <a-button
                  type="primary"
                  :loading="isLoading"
                  :disabled="!createForm.displayName"
                  @click="handleCreateRoom"
                  long
                >
                  创建会议
                </a-button>
              </a-form-item>
            </a-form>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>

    <!-- 会议信息 -->
    <a-modal
      v-model:visible="showRoomInfo"
      title="会议已创建"
      :width="500"
      :footer="false"
      :mask-closable="false"
    >
      <div class="room-info-content">
        <div class="room-info-section">
          <h4>会议号</h4>
          <a-input-group compact>
            <a-input
              :value="createdRoomId"
              readonly
              style="width: calc(100% - 80px)"
            />
            <a-button @click="copyRoomId" style="width: 80px">复制</a-button>
          </a-input-group>
        </div>

        <div v-if="createdRoomPassword" class="room-info-section">
          <h4>会议密码</h4>
          <a-input-group compact>
            <a-input
              :value="createdRoomPassword"
              readonly
              style="width: calc(100% - 80px)"
            />
            <a-button @click="copyRoomPassword" style="width: 80px">
              复制
            </a-button>
          </a-input-group>
        </div>

        <div class="room-info-section">
          <h4>分享链接</h4>
          <a-input-group compact>
            <a-input
              :value="shareLink"
              readonly
              style="width: calc(100% - 80px)"
            />
            <a-button @click="copyShareLink" style="width: 80px">复制</a-button>
          </a-input-group>
        </div>

        <div class="room-info-actions">
          <a-button type="primary" @click="enterCreatedRoom" long>
            进入会议
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useVideoConferenceStore } from "@/stores/videoConference";
import { Message } from "@arco-design/web-vue";
import { generateRoomId } from "@/utils/roomUtils";

// Props
interface Props {
  visible: boolean;
  canClose?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  canClose: true,
});

// Emits
const emit = defineEmits<{
  "update:visible": [value: boolean];
  "room-joined": [roomId: string, displayName: string];
}>();

// Store
const videoStore = useVideoConferenceStore();

// Local state
const visible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

const isLoading = ref(false);
const showRoomInfo = ref(false);
const createdRoomId = ref("");
const createdRoomPassword = ref("");

const joinForm = ref({
  displayName: "",
  roomId: "",
});

const createForm = ref({
  displayName: "220149567595776",
  topic: "",
  settings: {
    waitingRoom: false,
    muteOnEntry: true,
    requirePassword: false,
  },
  password: "",
});

// Computed
const shareLink = computed(() => {
  const baseUrl = window.location.origin;
  let link = `${baseUrl}?room=${createdRoomId.value}`;
  if (createdRoomPassword.value) {
    link += `&password=${encodeURIComponent(createdRoomPassword.value)}`;
  }
  return link;
});

// Methods
async function handleJoinRoom() {
  if (!joinForm.value.displayName || !joinForm.value.roomId) return;

  isLoading.value = true;
  try {
    // 初始化客户端
    await videoStore.initializeClient();

    // 创建本地流
    const streamSuccess = await videoStore.createLocalStream();
    if (!streamSuccess) {
      throw new Error("无法初始化摄像头和麦克风");
    }

    // 加入房间
    const success = await videoStore.joinRoom(
      joinForm.value.roomId,
      joinForm.value.displayName
    );

    if (success) {
      emit("room-joined", joinForm.value.roomId, joinForm.value.displayName);
      visible.value = false;
    } else {
      throw new Error("加入会议失败");
    }
  } catch (error: any) {
    Message.error(error.message || "加入会议失败");
  } finally {
    isLoading.value = false;
  }
}

async function handleCreateRoom() {
  if (!createForm.value.displayName) return;

  isLoading.value = true;
  try {
    // 生成房间ID
    const roomId = generateRoomId();
    createdRoomId.value = roomId;

    // 如果需要密码，设置密码
    if (
      createForm.value.settings.requirePassword &&
      createForm.value.password
    ) {
      createdRoomPassword.value = createForm.value.password;
    } else {
      createdRoomPassword.value = "";
    }

    // 显示房间信息
    showRoomInfo.value = true;
  } catch (error: any) {
    Message.error(error.message || "创建会议失败");
  } finally {
    isLoading.value = false;
  }
}

async function enterCreatedRoom() {
  // 使用创建的房间信息加入房间
  joinForm.value.displayName = createForm.value.displayName;
  joinForm.value.roomId = createdRoomId.value;

  await handleJoinRoom();
  showRoomInfo.value = false;
}

async function copyRoomId() {
  try {
    await navigator.clipboard.writeText(createdRoomId.value);
    Message.success("会议号已复制到剪贴板");
  } catch (error) {
    console.error("复制会议号失败:", error);
    Message.error("复制会议号失败");
  }
}

async function copyRoomPassword() {
  try {
    await navigator.clipboard.writeText(createdRoomPassword.value);
    Message.success("会议密码已复制到剪贴板");
  } catch (error) {
    console.error("复制会议密码失败:", error);
    Message.error("复制会议密码失败");
  }
}

async function copyShareLink() {
  try {
    await navigator.clipboard.writeText(shareLink.value);
    Message.success("分享链接已复制到剪贴板");
  } catch (error) {
    console.error("复制分享链接失败:", error);
    Message.error("复制分享链接失败");
  }
}
</script>

<style lang="scss" scoped>
.room-manager {
  .room-manager-content {
    padding: 16px 0;
  }

  .room-info-content {
    .room-info-section {
      margin-bottom: 24px;

      h4 {
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
      }
    }

    .room-info-actions {
      margin-top: 32px;
    }
  }
}
</style>
