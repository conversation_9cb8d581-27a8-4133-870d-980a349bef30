<template>
  <div class="device-settings">
    <a-modal
      v-model:visible="visible"
      title="Device Settings"
      :width="600"
      @ok="handleSave"
      @cancel="handleCancel"
    >
      <div class="settings-content">
        <!-- Camera Selection -->
        <div class="setting-group">
          <h4>Camera</h4>
          <a-select
            v-model="selectedCamera"
            placeholder="Select camera"
            style="width: 100%"
          >
            <a-option
              v-for="camera in availableDevices.cameras"
              :key="camera.deviceId"
              :value="camera.deviceId"
            >
              {{ camera.label || `Camera ${camera.deviceId.slice(0, 8)}` }}
            </a-option>
          </a-select>
          <div class="preview-container">
            <div id="camera-preview" class="preview-video"></div>
          </div>
        </div>

        <!-- Microphone Selection -->
        <div class="setting-group">
          <h4>Microphone</h4>
          <a-select
            v-model="selectedMicrophone"
            placeholder="Select microphone"
            style="width: 100%"
          >
            <a-option
              v-for="mic in availableDevices.microphones"
              :key="mic.deviceId"
              :value="mic.deviceId"
            >
              {{ mic.label || `Microphone ${mic.deviceId.slice(0, 8)}` }}
            </a-option>
          </a-select>
          <div class="audio-level-container">
            <div class="audio-level-bar">
              <div
                class="audio-level-fill"
                :style="{ width: `${audioLevel}%` }"
              ></div>
            </div>
            <span class="audio-level-text">{{ Math.round(audioLevel) }}%</span>
          </div>
        </div>

        <!-- Speaker Selection -->
        <div class="setting-group">
          <h4>Speaker</h4>
          <a-select
            v-model="selectedSpeaker"
            placeholder="Select speaker"
            style="width: 100%"
          >
            <a-option
              v-for="speaker in availableDevices.speakers"
              :key="speaker.deviceId"
              :value="speaker.deviceId"
            >
              {{ speaker.label || `Speaker ${speaker.deviceId.slice(0, 8)}` }}
            </a-option>
          </a-select>
          <a-button @click="testSpeaker" :loading="testingAudio">
            Test Speaker
          </a-button>
        </div>

        <!-- Video Quality Settings -->
        <div class="setting-group">
          <h4>Video Quality</h4>
          <a-select
            v-model="videoQuality"
            placeholder="Select video quality"
            style="width: 100%"
          >
            <a-option value="480p">480p (640x480)</a-option>
            <a-option value="720p">720p (1280x720)</a-option>
            <a-option value="1080p">1080p (1920x1080)</a-option>
          </a-select>
        </div>

        <!-- Audio Quality Settings -->
        <div class="setting-group">
          <h4>Audio Quality</h4>
          <a-select
            v-model="audioQuality"
            placeholder="Select audio quality"
            style="width: 100%"
          >
            <a-option value="speech">Speech (16kHz)</a-option>
            <a-option value="music">Music (48kHz)</a-option>
            <a-option value="high">High Quality (48kHz Stereo)</a-option>
          </a-select>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useVideoConferenceStore } from '@/stores/videoConference';
import { storeToRefs } from 'pinia';

// Props
interface Props {
  visible: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'save': [settings: any];
}>();

// Store
const videoStore = useVideoConferenceStore();
const { availableDevices, selectedDevices } = storeToRefs(videoStore);

// Local state
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const selectedCamera = ref(selectedDevices.value.cameraId);
const selectedMicrophone = ref(selectedDevices.value.microphoneId);
const selectedSpeaker = ref(selectedDevices.value.speakerId);
const videoQuality = ref('720p');
const audioQuality = ref('music');
const audioLevel = ref(0);
const testingAudio = ref(false);

let previewStream: any = null;
let audioLevelInterval: number | null = null;

// Methods
async function createPreviewStream() {
  try {
    if (previewStream) {
      previewStream.stop();
      await previewStream.destroy();
    }

    // Create a preview stream with selected devices
    const stream = await videoStore.createLocalStream();
    if (stream) {
      previewStream = stream;
      stream.play('camera-preview');
      startAudioLevelMonitoring();
    }
  } catch (error) {
    console.error('Failed to create preview stream:', error);
  }
}

function startAudioLevelMonitoring() {
  if (audioLevelInterval) {
    clearInterval(audioLevelInterval);
  }

  audioLevelInterval = setInterval(() => {
    if (previewStream && typeof previewStream.getAudioLevel === 'function') {
      const level = previewStream.getAudioLevel();
      audioLevel.value = Math.min(100, level * 100);
    }
  }, 100);
}

function stopAudioLevelMonitoring() {
  if (audioLevelInterval) {
    clearInterval(audioLevelInterval);
    audioLevelInterval = null;
  }
}

async function testSpeaker() {
  testingAudio.value = true;
  try {
    // Play a test sound
    const audio = new Audio('/test-audio.mp3');
    audio.volume = 0.5;
    await audio.play();
    setTimeout(() => {
      testingAudio.value = false;
    }, 2000);
  } catch (error) {
    console.error('Failed to test speaker:', error);
    testingAudio.value = false;
  }
}

function handleSave() {
  const settings = {
    cameraId: selectedCamera.value,
    microphoneId: selectedMicrophone.value,
    speakerId: selectedSpeaker.value,
    videoQuality: videoQuality.value,
    audioQuality: audioQuality.value,
  };

  // Update store
  selectedDevices.value.cameraId = selectedCamera.value;
  selectedDevices.value.microphoneId = selectedMicrophone.value;
  selectedDevices.value.speakerId = selectedSpeaker.value;

  emit('save', settings);
  visible.value = false;
}

function handleCancel() {
  // Reset to original values
  selectedCamera.value = selectedDevices.value.cameraId;
  selectedMicrophone.value = selectedDevices.value.microphoneId;
  selectedSpeaker.value = selectedDevices.value.speakerId;
  visible.value = false;
}

// Watchers
watch([selectedCamera, selectedMicrophone], () => {
  if (visible.value) {
    createPreviewStream();
  }
});

watch(visible, (newVisible) => {
  if (newVisible) {
    createPreviewStream();
  } else {
    if (previewStream) {
      previewStream.stop();
      previewStream.destroy();
      previewStream = null;
    }
    stopAudioLevelMonitoring();
  }
});

// Lifecycle
onMounted(() => {
  // Initialize with current settings
  selectedCamera.value = selectedDevices.value.cameraId;
  selectedMicrophone.value = selectedDevices.value.microphoneId;
  selectedSpeaker.value = selectedDevices.value.speakerId;
});

onUnmounted(() => {
  if (previewStream) {
    previewStream.stop();
    previewStream.destroy();
  }
  stopAudioLevelMonitoring();
});
</script>

<style lang="scss" scoped>
.device-settings {
  .settings-content {
    padding: 16px 0;
  }

  .setting-group {
    margin-bottom: 24px;

    h4 {
      margin-bottom: 8px;
      font-weight: 600;
      color: #333;
    }

    .a-select {
      margin-bottom: 12px;
    }
  }

  .preview-container {
    margin-top: 12px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    overflow: hidden;
    background: #000;
  }

  .preview-video {
    width: 100%;
    height: 200px;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
  }

  .audio-level-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 8px;
  }

  .audio-level-bar {
    flex: 1;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
  }

  .audio-level-fill {
    height: 100%;
    background: linear-gradient(to right, #52c41a, #faad14, #ff4d4f);
    transition: width 0.1s ease;
  }

  .audio-level-text {
    font-size: 12px;
    color: #666;
    min-width: 40px;
  }
}
</style>
