<template>
  <div class="video-conference">
    <!-- Room Manager -->
    <RoomManager
      v-model:visible="showJoinModal"
      :can-close="false"
      @room-joined="handleRoomJoined"
    />

    <!-- Main Conference Interface -->
    <div v-if="isConnected" class="conference-container">
      <!-- Video Grid -->
      <div class="video-grid">
        <!-- Local Video -->
        <div class="video-item local-video">
          <div class="video-wrapper">
            <div id="local-stream" class="video-element"></div>
            <div class="video-overlay">
              <span class="user-name">{{ displayName }} (You)</span>
              <div class="video-status">
                <a-tag v-if="!isAudioEnabled" color="red">
                  <template #icon>
                    <icon-mute />
                  </template>
                  Muted
                </a-tag>
                <a-tag v-if="!isVideoEnabled" color="orange">
                  <template #icon>
                    <icon-close />
                  </template>
                  Camera Off
                </a-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- Remote Videos -->
        <div
          v-for="stream in remoteStreamsList"
          :key="stream.getId()"
          class="video-item remote-video"
        >
          <div class="video-wrapper">
            <div
              :id="`remote-stream-${stream.getId()}`"
              class="video-element"
            ></div>
            <div class="video-overlay">
              <span class="user-name">{{ stream.getId() }}</span>
            </div>
          </div>
        </div>

        <!-- Screen Share -->
        <div v-if="isScreenSharing" class="video-item screen-share">
          <div class="video-wrapper">
            <div id="screen-share" class="video-element"></div>
            <div class="video-overlay">
              <span class="user-name">Screen Share</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Control Panel -->
      <div class="control-panel">
        <div class="controls">
          <!-- Audio Toggle -->
          <a-button
            :type="isAudioEnabled ? 'primary' : 'secondary'"
            :status="isAudioEnabled ? 'normal' : 'danger'"
            shape="circle"
            size="large"
            @click="toggleAudio"
          >
            <template #icon>
              <icon-sound v-if="isAudioEnabled" />
              <icon-mute v-else />
            </template>
          </a-button>

          <!-- Video Toggle -->
          <a-button
            :type="isVideoEnabled ? 'primary' : 'secondary'"
            :status="isVideoEnabled ? 'normal' : 'danger'"
            shape="circle"
            size="large"
            @click="toggleVideo"
          >
            <template #icon>
              <icon-camera v-if="isVideoEnabled" />
              <icon-close v-else />
            </template>
          </a-button>

          <!-- Screen Share Toggle -->
          <a-button shape="circle" size="large" @click="toggleScreenShare">
            <template #icon>
              <icon-desktop />
            </template>
          </a-button>

          <!-- Device Settings -->
          <a-button
            type="outline"
            shape="circle"
            size="large"
            @click="showDeviceSettings = true"
          >
            <template #icon>
              <icon-settings />
            </template>
          </a-button>

          <!-- Participants -->
          <a-button
            type="outline"
            shape="circle"
            size="large"
            @click="showParticipantList = true"
          >
            <template #icon>
              <icon-user-group />
            </template>
          </a-button>

          <!-- Leave Room -->
          <a-button
            type="primary"
            status="danger"
            shape="circle"
            size="large"
            @click="handleLeaveRoom"
          >
            <template #icon>
              <icon-phone />
            </template>
          </a-button>
        </div>

        <!-- Room Info -->
        <div class="room-info">
          <a-tag color="blue">Room: {{ roomId }}</a-tag>
          <a-tag color="green"
            >{{ remoteStreamsList.length + 1 }} participants</a-tag
          >
          <a-button size="small" @click="debugLocalStream">调试本地流</a-button>
        </div>
      </div>
    </div>

    <!-- Error Message -->
    <a-alert
      v-if="hasError"
      :message="error"
      type="error"
      closable
      @close="clearError"
      style="margin-bottom: 16px"
    />

    <!-- Device Settings Modal -->
    <DeviceSettings v-model:visible="showDeviceSettings" />

    <!-- Participant List Drawer -->
    <ParticipantList v-model:visible="showParticipantList" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import { useVideoConferenceStore } from "@/stores/videoConference";
import { storeToRefs } from "pinia";
import DeviceSettings from "./DeviceSettings.vue";
import ParticipantList from "./ParticipantList.vue";
import RoomManager from "./RoomManager.vue";
import {
  IconSound,
  IconMute,
  IconCamera,
  IconClose,
  IconDesktop,
  IconSettings,
  IconUserGroup,
} from "@arco-design/web-vue/es/icon";

// Store
const videoStore = useVideoConferenceStore();
const {
  roomId,
  displayName,
  remoteStreamsList,
  isAudioEnabled,
  isVideoEnabled,
  isScreenSharing,
  error,
  isConnected,
  isConnecting,
  hasError,
} = storeToRefs(videoStore);

// Local state
const showJoinModal = ref(true);
const showDeviceSettings = ref(false);
const showParticipantList = ref(false);
const joinForm = ref({
  displayName: "",
  roomId: "",
});

// Methods
async function handleRoomJoined(roomId: string, displayName: string) {
  // 房间已经在RoomManager组件中加入，这里只需要处理UI状态
  showJoinModal.value = false;

  // 确保本地流在UI中播放
  await playLocalStream();
}

async function playLocalStream() {
  // 等待DOM更新
  await nextTick();

  const localStreamElement = document.getElementById("local-stream");
  console.log("本地流元素:", localStreamElement);
  console.log("本地流对象:", videoStore.localStream);

  if (videoStore.localStream && localStreamElement) {
    try {
      // 清空元素内容
      localStreamElement.innerHTML = "";

      // 播放本地流
      videoStore.localStream.play("local-stream");
      console.log("本地流开始播放到元素:", localStreamElement);

      // 设置观察器监听video元素的添加
      setupVideoElementObserver(localStreamElement);

      // 多次检查video元素的创建
      const checkVideoElement = (attempt = 1) => {
        const videoElement = localStreamElement.querySelector("video");
        console.log(`第${attempt}次检查视频元素:`, videoElement);

        if (videoElement) {
          setupVideoElement(videoElement);
        } else if (attempt < 5) {
          // 如果没有找到video元素，继续尝试
          setTimeout(() => checkVideoElement(attempt + 1), 500);
        } else {
          console.error("5次尝试后仍未找到video元素");
          // 尝试手动创建video元素
          createManualVideoElement(localStreamElement);
        }
      };

      setTimeout(() => checkVideoElement(), 500);
    } catch (error) {
      console.error("播放本地流失败:", error);
    }
  } else {
    console.warn("本地流或元素不存在:", {
      localStream: !!videoStore.localStream,
      element: !!localStreamElement,
    });
  }
}

// 设置视频元素观察器
function setupVideoElementObserver(container: HTMLElement) {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement;
          const videoElement =
            element.tagName === "VIDEO"
              ? (element as HTMLVideoElement)
              : element.querySelector("video");

          if (videoElement) {
            console.log("检测到video元素被添加:", videoElement);
            setupVideoElement(videoElement);
          }
        }
      });
    });
  });

  observer.observe(container, {
    childList: true,
    subtree: true,
  });

  // 5秒后断开观察器
  setTimeout(() => {
    observer.disconnect();
  }, 5000);
}

// 设置video元素属性
function setupVideoElement(videoElement: HTMLVideoElement) {
  console.log("设置video元素属性");

  // 强制设置样式
  videoElement.style.width = "100%";
  videoElement.style.height = "100%";
  videoElement.style.objectFit = "cover";
  videoElement.style.display = "block";
  videoElement.style.minWidth = "100%";
  videoElement.style.minHeight = "100%";
  videoElement.style.backgroundColor = "#000";

  // 设置属性
  videoElement.autoplay = true;
  videoElement.muted = true;
  videoElement.playsInline = true;

  // 检查srcObject是否存在
  if (!videoElement.srcObject && videoStore.localStream) {
    try {
      // 尝试直接设置srcObject
      const mediaStream = videoStore.localStream.getMediaStream?.();
      if (mediaStream) {
        console.log("直接设置MediaStream到video元素:", mediaStream);
        videoElement.srcObject = mediaStream;
      } else {
        console.warn("无法获取MediaStream对象");
      }
    } catch (err) {
      console.error("设置srcObject失败:", err);
    }
  }

  // 尝试播放
  if (videoElement.paused) {
    videoElement.play().catch((err) => {
      console.error("播放视频失败:", err);
    });
  }

  console.log("Video元素设置完成:", {
    clientWidth: videoElement.clientWidth,
    clientHeight: videoElement.clientHeight,
    offsetWidth: videoElement.offsetWidth,
    offsetHeight: videoElement.offsetHeight,
    videoWidth: videoElement.videoWidth,
    videoHeight: videoElement.videoHeight,
    srcObject: !!videoElement.srcObject,
  });
}

// 手动创建video元素
function createManualVideoElement(container: HTMLElement) {
  console.log("手动创建video元素");

  try {
    // 清空容器
    container.innerHTML = "";

    // 创建video元素
    const videoElement = document.createElement("video");
    videoElement.autoplay = true;
    videoElement.muted = true;
    videoElement.playsInline = true;
    videoElement.style.width = "100%";
    videoElement.style.height = "100%";
    videoElement.style.objectFit = "cover";
    videoElement.style.display = "block";
    videoElement.style.backgroundColor = "#000";

    // 尝试获取MediaStream
    if (videoStore.localStream) {
      try {
        const mediaStream = videoStore.localStream.getMediaStream?.();
        if (mediaStream) {
          console.log("设置MediaStream到手动创建的video元素:", mediaStream);
          videoElement.srcObject = mediaStream;
        } else {
          console.warn("无法从localStream获取MediaStream");
          // 尝试其他方法获取流
          if (videoStore.localStream.stream) {
            videoElement.srcObject = videoStore.localStream.stream;
          } else if (videoStore.localStream._stream) {
            videoElement.srcObject = videoStore.localStream._stream;
          }
        }
      } catch (err) {
        console.error("获取MediaStream失败:", err);
      }
    }

    // 添加到容器
    container.appendChild(videoElement);

    // 尝试播放
    videoElement.play().catch((err) => {
      console.error("播放手动创建的视频失败:", err);
    });

    console.log("手动创建的video元素已添加到容器");
  } catch (err) {
    console.error("手动创建video元素失败:", err);
  }
}

async function handleLeaveRoom() {
  await videoStore.leaveRoom();
  showJoinModal.value = true;
}

async function toggleAudio() {
  await videoStore.toggleAudio();
}

async function toggleVideo() {
  await videoStore.toggleVideo();
}

async function toggleScreenShare() {
  if (isScreenSharing.value) {
    await videoStore.stopScreenSharing();
  } else {
    await videoStore.startScreenSharing();
  }
}

function clearError() {
  videoStore.clearError();
}

function debugLocalStream() {
  console.log("=== 调试本地流 ===");
  console.log("连接状态:", isConnected.value);
  console.log("本地流对象:", videoStore.localStream);
  console.log("音频状态:", isAudioEnabled.value);
  console.log("视频状态:", isVideoEnabled.value);

  const localStreamElement = document.getElementById("local-stream");
  console.log("本地流DOM元素:", localStreamElement);

  // 检查本地流的详细信息
  if (videoStore.localStream) {
    console.log("本地流详细信息:", {
      hasAudio: videoStore.localStream.hasAudio?.(),
      hasVideo: videoStore.localStream.hasVideo?.(),
      getMediaStream: typeof videoStore.localStream.getMediaStream,
      stream: videoStore.localStream.stream,
      _stream: videoStore.localStream._stream,
    });

    // 尝试获取MediaStream
    try {
      const mediaStream = videoStore.localStream.getMediaStream?.();
      if (mediaStream) {
        console.log("MediaStream信息:", {
          id: mediaStream.id,
          active: mediaStream.active,
          videoTracks: mediaStream.getVideoTracks().length,
          audioTracks: mediaStream.getAudioTracks().length,
        });

        mediaStream
          .getVideoTracks()
          .forEach((track: MediaStreamTrack, index: number) => {
            console.log(`视频轨道${index}:`, {
              id: track.id,
              kind: track.kind,
              enabled: track.enabled,
              readyState: track.readyState,
              settings: track.getSettings?.(),
            });
          });
      }
    } catch (err) {
      console.error("获取MediaStream失败:", err);
    }
  }

  if (localStreamElement) {
    const videoElement = localStreamElement.querySelector("video");
    console.log("视频元素:", videoElement);

    if (videoElement) {
      console.log("视频元素详细信息:", {
        src: videoElement.src,
        srcObject: videoElement.srcObject,
        videoWidth: videoElement.videoWidth,
        videoHeight: videoElement.videoHeight,
        readyState: videoElement.readyState,
        paused: videoElement.paused,
        muted: videoElement.muted,
        autoplay: videoElement.autoplay,
        clientWidth: videoElement.clientWidth,
        clientHeight: videoElement.clientHeight,
        offsetWidth: videoElement.offsetWidth,
        offsetHeight: videoElement.offsetHeight,
      });

      // 重新设置video元素
      setupVideoElement(videoElement);
    }
  }

  // 尝试重新播放本地流
  playLocalStream();
}

// Lifecycle
onMounted(async () => {
  await videoStore.initializeClient();

  // 如果已经连接，尝试播放本地流
  if (isConnected.value) {
    await playLocalStream();
  }

  // 监听窗口大小变化，重新播放本地流
  window.addEventListener("resize", handleResize);
});

onUnmounted(async () => {
  if (isConnected.value) {
    await videoStore.leaveRoom();
  }

  window.removeEventListener("resize", handleResize);
});

// 处理窗口大小变化
function handleResize() {
  if (isConnected.value) {
    // 延迟执行，确保DOM已更新
    setTimeout(() => {
      playLocalStream();
    }, 300);
  }
}
</script>

<style lang="scss" scoped>
.video-conference {
  height: 100vh;
  background: #f5f5f5;
  position: relative;
}

.join-form {
  padding: 20px 0;
}

.conference-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-grid {
  flex: 1;
  display: grid;
  gap: 16px;
  padding: 16px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-auto-rows: minmax(200px, 1fr);
  overflow-y: auto;
}

.video-item {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  min-height: 200px;

  &.local-video {
    border: 2px solid #1890ff;
  }

  &.screen-share {
    grid-column: 1 / -1;
    min-height: 400px;
  }
}

.video-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.video-element {
  width: 100%;
  height: 100%;
  min-height: 200px;
  background: #000;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  /* 确保视频元素正确显示 */
  & > video {
    width: 100% !important;
    height: 100% !important;
    min-width: 100%;
    min-height: 100%;
    object-fit: cover;
    display: block !important;
    background: #000;
  }

  /* 如果没有视频，显示占位符 */
  &:empty::before {
    content: "等待视频...";
    color: #fff;
    font-size: 14px;
  }
}

.video-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.user-name {
  color: white;
  font-weight: 500;
}

.video-status {
  display: flex;
  gap: 8px;
}

.control-panel {
  background: white;
  padding: 16px;
  border-top: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.room-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

@media (max-width: 768px) {
  .video-grid {
    grid-template-columns: 1fr;
    padding: 8px;
    gap: 8px;
  }

  .control-panel {
    flex-direction: column;
    gap: 16px;
  }

  .controls {
    justify-content: center;
  }
}
</style>
