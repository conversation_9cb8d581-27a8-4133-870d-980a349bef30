<template>
  <div class="video-conference">
    <!-- Room Manager -->
    <RoomManager
      v-model:visible="showJoinModal"
      :can-close="false"
      @room-joined="handleRoomJoined"
    />

    <!-- Main Conference Interface -->
    <div v-if="isConnected" class="conference-container">
      <!-- Video Grid -->
      <div class="video-grid">
        <!-- Local Video -->
        <div class="video-item local-video">
          <div class="video-wrapper">
            <div
              id="local-stream"
              class="video-element"
              ref="localVideoElement"
            ></div>
            <div class="video-overlay">
              <span class="user-name">{{ displayName }} (You)</span>
              <div class="video-status">
                <a-tag v-if="!isAudioEnabled" color="red">
                  <template #icon>
                    <icon-mute />
                  </template>
                  Muted
                </a-tag>
                <a-tag v-if="!isVideoEnabled" color="orange">
                  <template #icon>
                    <icon-close />
                  </template>
                  Camera Off
                </a-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- Remote Videos -->
        <div
          v-for="stream in remoteStreamsList"
          :key="stream.getId()"
          class="video-item remote-video"
        >
          <div class="video-wrapper">
            <div
              :id="`remote-stream-${stream.getId()}`"
              class="video-element"
            ></div>
            <div class="video-overlay">
              <span class="user-name">{{ stream.getId() }}</span>
            </div>
          </div>
        </div>

        <!-- Screen Share -->
        <div v-if="isScreenSharing" class="video-item screen-share">
          <div class="video-wrapper">
            <div
              id="screen-share"
              class="video-element"
              ref="screenShareElement"
            ></div>
            <div class="video-overlay">
              <span class="user-name">Screen Share</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Control Panel -->
      <div class="control-panel">
        <div class="controls">
          <!-- Audio Toggle -->
          <a-button
            :type="isAudioEnabled ? 'primary' : 'secondary'"
            :status="isAudioEnabled ? 'normal' : 'danger'"
            shape="circle"
            size="large"
            @click="toggleAudio"
          >
            <template #icon>
              <icon-sound v-if="isAudioEnabled" />
              <icon-mute v-else />
            </template>
          </a-button>

          <!-- Video Toggle -->
          <a-button
            :type="isVideoEnabled ? 'primary' : 'secondary'"
            :status="isVideoEnabled ? 'normal' : 'danger'"
            shape="circle"
            size="large"
            @click="toggleVideo"
          >
            <template #icon>
              <icon-camera v-if="isVideoEnabled" />
              <icon-close v-else />
            </template>
          </a-button>

          <!-- Screen Share Toggle -->
          <a-button shape="circle" size="large" @click="toggleScreenShare">
            <template #icon>
              <icon-desktop />
            </template>
          </a-button>

          <!-- Device Settings -->
          <a-button
            type="outline"
            shape="circle"
            size="large"
            @click="showDeviceSettings = true"
          >
            <template #icon>
              <icon-settings />
            </template>
          </a-button>

          <!-- Participants -->
          <a-button
            type="outline"
            shape="circle"
            size="large"
            @click="showParticipantList = true"
          >
            <template #icon>
              <icon-user-group />
            </template>
          </a-button>

          <!-- Leave Room -->
          <a-button
            type="primary"
            status="danger"
            shape="circle"
            size="large"
            @click="handleLeaveRoom"
          >
            <template #icon>
              <icon-phone />
            </template>
          </a-button>
        </div>

        <!-- Room Info -->
        <div class="room-info">
          <a-tag color="blue">Room: {{ roomId }}</a-tag>
          <a-tag color="green"
            >{{ remoteStreamsList.length + 1 }} participants</a-tag
          >
          <a-button size="small" @click="debugLocalStream">调试本地流</a-button>
          <a-button size="small" @click="debugScreenShare"
            >调试屏幕共享</a-button
          >
        </div>
      </div>
    </div>

    <!-- Error Message -->
    <a-alert
      v-if="hasError"
      :message="error"
      type="error"
      closable
      @close="clearError"
      style="margin-bottom: 16px"
    />

    <!-- Device Settings Modal -->
    <DeviceSettings v-model:visible="showDeviceSettings" />

    <!-- Participant List Drawer -->
    <ParticipantList v-model:visible="showParticipantList" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch } from "vue";
import { useVideoConferenceStore } from "@/stores/videoConference";
import { storeToRefs } from "pinia";
import DeviceSettings from "./DeviceSettings.vue";
import ParticipantList from "./ParticipantList.vue";
import RoomManager from "./RoomManager.vue";
import {
  IconSound,
  IconMute,
  IconCamera,
  IconClose,
  IconDesktop,
  IconSettings,
  IconUserGroup,
} from "@arco-design/web-vue/es/icon";

// Store
const videoStore = useVideoConferenceStore();
const {
  roomId,
  displayName,
  screenStream,
  remoteStreamsList,
  isAudioEnabled,
  isVideoEnabled,
  isScreenSharing,
  error,
  isConnected,
  isConnecting,
  hasError,
} = storeToRefs(videoStore);

// Local state
const showJoinModal = ref(true);
const showDeviceSettings = ref(false);
const showParticipantList = ref(false);
const localVideoElement = ref<HTMLElement>();
const screenShareElement = ref<HTMLElement>();
const localUid = ref<string>("");

// Methods
async function handleRoomJoined(roomId: string, displayName: string) {
  // 房间已经在RoomManager组件中加入，这里只需要处理UI状态
  showJoinModal.value = false;

  // 设置本地用户ID
  localUid.value = displayName;

  // 确保本地流在UI中播放
  await playLocalStream();
}

async function playLocalStream() {
  // 等待DOM更新
  await nextTick();

  console.log("开始播放本地流");
  console.log("本地流对象:", videoStore.localStream);
  console.log("本地视频元素:", localVideoElement.value);

  if (videoStore.localStream && localVideoElement.value) {
    try {
      // 使用NERTC标准播放方式
      await playStream(localVideoElement.value, videoStore.localStream, {
        renderMode: { cut: false },
        mediaType: "video",
      });
    } catch (error) {
      console.error("播放本地流失败:", error);
    }
  } else {
    console.warn("本地流或视频元素不存在:", {
      localStream: !!videoStore.localStream,
      element: !!localVideoElement.value,
    });
  }
}

/**
 * @description 播放音视频流
 * @param {HTMLElement} el
 * @param {any} stream - NERTC流对象
 * @param {Object} options - 播放选项
 */
async function playStream(
  el: HTMLElement,
  stream: any,
  options?: {
    renderMode?: { width?: number; height?: number; cut?: boolean };
    mediaType?: string;
    playOptions?: any;
  }
) {
  const uid = stream.getId() as string;
  console.log(`播放音视频流，uid: ${uid}`, el, stream, options);

  if (!el) {
    console.log(`播放音视频失败，因该${uid}播放元素不存在`);
    return;
  }

  const { clientWidth: width, clientHeight: height } = el;

  // 为屏幕共享设置特殊的渲染模式
  let defRenderMode;
  if (el.id === "screen-share") {
    // 屏幕共享使用固定尺寸，确保有足够的空间
    defRenderMode = {
      width: Math.max(width, 800),
      height: Math.max(height, 400),
      cut: false,
    };
  } else {
    defRenderMode = { width, height, cut: false };
  }

  const mergeRenderMode = Object.assign(defRenderMode, options?.renderMode);
  console.log("播放音视频流，渲染模式", mergeRenderMode);
  console.log("元素尺寸:", { width, height, elementId: el.id });

  // 设置本地用户ID
  if (!localUid.value) {
    localUid.value = displayName.value || "local";
  }

  try {
    if (uid === localUid.value) {
      // 设置本地视频画布
      if (typeof stream.setLocalRenderMode === "function") {
        stream.setLocalRenderMode(mergeRenderMode, options?.mediaType);
      }
    } else {
      // 设置远程视频画布
      if (typeof stream.setRemoteRenderMode === "function") {
        stream.setRemoteRenderMode(mergeRenderMode, options?.mediaType);
      }
    }

    // 播放流
    await stream.play(el, options?.playOptions);
    console.log(`流播放成功，uid: ${uid}`);
  } catch (error) {
    console.error(`流播放失败，uid: ${uid}`, error);
    throw error;
  }
}

async function handleLeaveRoom() {
  // 停止本地流播放
  if (videoStore.localStream && localVideoElement.value) {
    try {
      videoStore.localStream.stop();
    } catch (error) {
      console.error("停止本地流失败:", error);
    }
  }

  await videoStore.leaveRoom();
  showJoinModal.value = true;
}

async function toggleAudio() {
  await videoStore.toggleAudio();
}

async function toggleVideo() {
  await videoStore.toggleVideo();
}

async function toggleScreenShare() {
  if (isScreenSharing.value) {
    await videoStore.stopScreenSharing();
  } else {
    await videoStore.startScreenSharing();

    // 等待屏幕共享流创建完成后播放
    await nextTick();
    console.log("屏幕共享开启后检查:", {
      screenStream: !!screenStream.value,
      screenShareElement: !!screenShareElement.value,
      isScreenSharing: isScreenSharing.value,
    });

    // 注意：这里不需要手动播放，因为watch会自动处理
    // 但我们可以添加一个备用的播放逻辑
    setTimeout(async () => {
      if (
        screenStream.value &&
        screenShareElement.value &&
        !screenShareElement.value.innerHTML
      ) {
        console.log("备用播放屏幕共享流");
        try {
          await playStream(screenShareElement.value, screenStream.value, {
            renderMode: {
              width: Math.max(screenShareElement.value.clientWidth, 800),
              height: Math.max(screenShareElement.value.clientHeight, 400),
              cut: false,
            },
            mediaType: "video",
          });
          console.log("备用屏幕共享流播放成功");
        } catch (error) {
          console.error("备用屏幕共享流播放失败:", error);
        }
      }
    }, 500);
  }
}

function clearError() {
  videoStore.clearError();
}

function debugLocalStream() {
  console.log("=== 调试本地流 ===");
  console.log("连接状态:", isConnected.value);
  console.log("本地流对象:", videoStore.localStream);
  console.log("屏幕共享流对象:", screenStream.value);
  console.log("音频状态:", isAudioEnabled.value);
  console.log("视频状态:", isVideoEnabled.value);
  console.log("屏幕共享状态:", isScreenSharing.value);

  const localStreamElement = document.getElementById("local-stream");
  console.log("本地流DOM元素:", localStreamElement);

  // 检查本地流的详细信息
  if (videoStore.localStream) {
    console.log("本地流对象所有属性:", Object.keys(videoStore.localStream));
    console.log("本地流详细信息:", {
      hasAudio: videoStore.localStream.hasAudio?.(),
      hasVideo: videoStore.localStream.hasVideo?.(),
      getMediaStream: typeof videoStore.localStream.getMediaStream,
      stream: videoStore.localStream.stream,
      _stream: videoStore.localStream._stream,
      localStream: videoStore.localStream.localStream,
      mediaStream: videoStore.localStream.mediaStream,
    });

    // 尝试获取MediaStream
    try {
      const mediaStream = videoStore.localStream.getMediaStream?.();
      if (mediaStream) {
        console.log("MediaStream信息:", {
          id: mediaStream.id,
          active: mediaStream.active,
          videoTracks: mediaStream.getVideoTracks().length,
          audioTracks: mediaStream.getAudioTracks().length,
        });

        mediaStream
          .getVideoTracks()
          .forEach((track: MediaStreamTrack, index: number) => {
            console.log(`视频轨道${index}:`, {
              id: track.id,
              kind: track.kind,
              enabled: track.enabled,
              readyState: track.readyState,
              settings: track.getSettings?.(),
            });
          });
      }
    } catch (err) {
      console.error("获取MediaStream失败:", err);
    }
  }

  if (localStreamElement) {
    const videoElement = localStreamElement.querySelector("video");
    console.log("视频元素:", videoElement);

    if (videoElement) {
      console.log("视频元素详细信息:", {
        src: videoElement.src,
        srcObject: videoElement.srcObject,
        videoWidth: videoElement.videoWidth,
        videoHeight: videoElement.videoHeight,
        readyState: videoElement.readyState,
        paused: videoElement.paused,
        muted: videoElement.muted,
        autoplay: videoElement.autoplay,
        clientWidth: videoElement.clientWidth,
        clientHeight: videoElement.clientHeight,
        offsetWidth: videoElement.offsetWidth,
        offsetHeight: videoElement.offsetHeight,
      });

      // 尝试重新播放本地流
      playLocalStream();
    }
  }

  // 检查视频元素状态
  if (localVideoElement.value) {
    console.log("视频元素信息:", {
      clientWidth: localVideoElement.value.clientWidth,
      clientHeight: localVideoElement.value.clientHeight,
      innerHTML: localVideoElement.value.innerHTML,
    });
  }

  // 尝试重新播放本地流
  playLocalStream();
}

function debugScreenShare() {
  console.log("=== 调试屏幕共享 ===");
  console.log("屏幕共享状态:", isScreenSharing.value);
  console.log("屏幕共享流对象:", screenStream.value);
  console.log("屏幕共享元素:", screenShareElement.value);

  if (screenShareElement.value) {
    console.log("屏幕共享元素信息:", {
      id: screenShareElement.value.id,
      clientWidth: screenShareElement.value.clientWidth,
      clientHeight: screenShareElement.value.clientHeight,
      innerHTML: screenShareElement.value.innerHTML,
      children: screenShareElement.value.children.length,
    });

    // 检查是否有video元素
    const videoElement = screenShareElement.value.querySelector("video");
    if (videoElement) {
      console.log("屏幕共享video元素:", {
        videoWidth: videoElement.videoWidth,
        videoHeight: videoElement.videoHeight,
        clientWidth: videoElement.clientWidth,
        clientHeight: videoElement.clientHeight,
        readyState: videoElement.readyState,
        paused: videoElement.paused,
      });
    } else {
      console.log("未找到屏幕共享video元素");
    }
  }

  if (screenStream.value) {
    console.log("屏幕共享流详细信息:", {
      id: screenStream.value.getId?.(),
      hasVideo: screenStream.value.hasVideo?.(),
      hasAudio: screenStream.value.hasAudio?.(),
    });
  }

  // 尝试重新播放屏幕共享流
  if (screenStream.value && screenShareElement.value) {
    console.log("尝试重新播放屏幕共享流");
    playStream(screenShareElement.value, screenStream.value, {
      renderMode: {
        width: Math.max(screenShareElement.value.clientWidth, 800),
        height: Math.max(screenShareElement.value.clientHeight, 400),
        cut: false,
      },
      mediaType: "video",
    })
      .then(() => {
        console.log("重新播放屏幕共享流成功");
      })
      .catch((error) => {
        console.error("重新播放屏幕共享流失败:", error);
      });
  }
}

// 监听屏幕共享状态变化
watch(isScreenSharing, async (newValue) => {
  console.log("屏幕共享状态变化:", newValue);
  if (newValue && screenStream.value && screenShareElement.value) {
    console.log("准备播放屏幕共享流:", {
      screenStream: !!screenStream.value,
      screenShareElement: !!screenShareElement.value,
      elementSize: {
        clientWidth: screenShareElement.value.clientWidth,
        clientHeight: screenShareElement.value.clientHeight,
      },
    });

    // 延迟一下确保DOM已更新
    setTimeout(async () => {
      try {
        await playStream(screenShareElement.value!, screenStream.value!, {
          renderMode: {
            width: Math.max(screenShareElement.value!.clientWidth, 800),
            height: Math.max(screenShareElement.value!.clientHeight, 400),
            cut: false,
          },
          mediaType: "video",
        });
        console.log("屏幕共享流自动播放成功");
      } catch (error) {
        console.error("屏幕共享流自动播放失败:", error);
      }
    }, 200);
  }
});

// Lifecycle
onMounted(async () => {
  await videoStore.initializeClient();

  // 如果已经连接，尝试播放本地流
  if (isConnected.value) {
    await playLocalStream();
  }

  // 监听窗口大小变化，重新播放本地流
  window.addEventListener("resize", handleResize);
});

onUnmounted(async () => {
  if (isConnected.value) {
    await videoStore.leaveRoom();
  }

  window.removeEventListener("resize", handleResize);
});

// 处理窗口大小变化
function handleResize() {
  if (isConnected.value && localVideoElement.value) {
    // 延迟执行，确保DOM已更新
    setTimeout(() => {
      playLocalStream();
    }, 300);
  }
}
</script>

<style lang="scss" scoped>
.video-conference {
  height: 100vh;
  background: #f5f5f5;
  position: relative;
}

.join-form {
  padding: 20px 0;
}

.conference-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-grid {
  flex: 1;
  display: grid;
  gap: 16px;
  padding: 16px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-auto-rows: minmax(200px, 1fr);
  overflow-y: auto;
}

.video-item {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  min-height: 200px;

  &.local-video {
    border: 2px solid #1890ff;
  }

  &.screen-share {
    grid-column: 1 / -1;
    min-height: 400px;

    .video-element {
      min-height: 400px;
      height: 400px;
    }
  }
}

.video-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.video-element {
  width: 100%;
  height: 100%;
  min-height: 200px;
  background: #000;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  /* 确保视频元素正确显示 */
  & > video {
    width: 100% !important;
    height: 100% !important;
    min-width: 100%;
    min-height: 100%;
    object-fit: cover;
    display: block !important;
    background: #000;
  }

  /* 如果没有视频，显示占位符 */
  &:empty::before {
    content: "等待视频...";
    color: #fff;
    font-size: 14px;
  }
}

.video-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.user-name {
  color: white;
  font-weight: 500;
}

.video-status {
  display: flex;
  gap: 8px;
}

.control-panel {
  background: white;
  padding: 16px;
  border-top: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.room-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

@media (max-width: 768px) {
  .video-grid {
    grid-template-columns: 1fr;
    padding: 8px;
    gap: 8px;
  }

  .control-panel {
    flex-direction: column;
    gap: 16px;
  }

  .controls {
    justify-content: center;
  }
}
</style>
