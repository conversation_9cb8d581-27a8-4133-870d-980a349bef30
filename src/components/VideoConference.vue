<template>
  <div class="video-conference">
    <!-- Join Room Modal -->
    <a-modal
      v-model:visible="showJoinModal"
      title="Join Video Conference"
      :closable="false"
      :mask-closable="false"
      :footer="false"
    >
      <div class="join-form">
        <a-form :model="joinForm" layout="vertical" @submit="handleJoinRoom">
          <a-form-item label="Display Name" required>
            <a-input
              v-model="joinForm.displayName"
              placeholder="Enter your name"
              :disabled="isConnecting"
            />
          </a-form-item>
          <a-form-item label="Room ID" required>
            <a-input
              v-model="joinForm.roomId"
              placeholder="Enter room ID"
              :disabled="isConnecting"
            />
          </a-form-item>
          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              :loading="isConnecting"
              :disabled="!joinForm.displayName || !joinForm.roomId"
              block
            >
              {{ isConnecting ? "Joining..." : "Join Room" }}
            </a-button>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- Main Conference Interface -->
    <div v-if="isConnected" class="conference-container">
      <!-- Video Grid -->
      <div class="video-grid">
        <!-- Local Video -->
        <div class="video-item local-video">
          <div class="video-wrapper">
            <div id="local-stream" class="video-element"></div>
            <div class="video-overlay">
              <span class="user-name">{{ displayName }} (You)</span>
              <div class="video-status">
                <a-tag v-if="!isAudioEnabled" color="red">
                  <template #icon>
                    <icon-mute />
                  </template>
                  Muted
                </a-tag>
                <a-tag v-if="!isVideoEnabled" color="orange">
                  <template #icon>
                    <icon-camera-off />
                  </template>
                  Camera Off
                </a-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- Remote Videos -->
        <div
          v-for="stream in remoteStreamsList"
          :key="stream.getId()"
          class="video-item remote-video"
        >
          <div class="video-wrapper">
            <div
              :id="`remote-stream-${stream.getId()}`"
              class="video-element"
            ></div>
            <div class="video-overlay">
              <span class="user-name">{{ stream.getId() }}</span>
            </div>
          </div>
        </div>

        <!-- Screen Share -->
        <div v-if="isScreenSharing" class="video-item screen-share">
          <div class="video-wrapper">
            <div id="screen-share" class="video-element"></div>
            <div class="video-overlay">
              <span class="user-name">Screen Share</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Control Panel -->
      <div class="control-panel">
        <div class="controls">
          <!-- Audio Toggle -->
          <a-button
            :type="isAudioEnabled ? 'primary' : 'secondary'"
            :status="isAudioEnabled ? 'normal' : 'danger'"
            shape="circle"
            size="large"
            @click="toggleAudio"
          >
            <template #icon>
              <icon-sound v-if="isAudioEnabled" />
              <icon-mute v-else />
            </template>
          </a-button>

          <!-- Video Toggle -->
          <a-button
            :type="isVideoEnabled ? 'primary' : 'secondary'"
            :status="isVideoEnabled ? 'normal' : 'danger'"
            shape="circle"
            size="large"
            @click="toggleVideo"
          >
            <template #icon>
              <icon-camera v-if="isVideoEnabled" />
              <icon-camera-off v-else />
            </template>
          </a-button>

          <!-- Screen Share Toggle -->
          <a-button
            :type="isScreenSharing ? 'primary' : 'secondary'"
            shape="circle"
            size="large"
            @click="toggleScreenShare"
          >
            <template #icon>
              <icon-desktop />
            </template>
          </a-button>

          <!-- Device Settings -->
          <a-button
            type="secondary"
            shape="circle"
            size="large"
            @click="showDeviceSettings = true"
          >
            <template #icon>
              <icon-settings />
            </template>
          </a-button>

          <!-- Participants -->
          <a-button
            type="secondary"
            shape="circle"
            size="large"
            @click="showParticipantList = true"
          >
            <template #icon>
              <icon-user-group />
            </template>
          </a-button>

          <!-- Leave Room -->
          <a-button
            type="primary"
            status="danger"
            shape="circle"
            size="large"
            @click="handleLeaveRoom"
          >
            <template #icon>
              <icon-phone />
            </template>
          </a-button>
        </div>

        <!-- Room Info -->
        <div class="room-info">
          <a-tag color="blue">Room: {{ roomId }}</a-tag>
          <a-tag color="green"
            >{{ remoteStreamsList.length + 1 }} participants</a-tag
          >
        </div>
      </div>
    </div>

    <!-- Error Message -->
    <a-alert
      v-if="hasError"
      :message="error"
      type="error"
      closable
      @close="clearError"
      style="margin-bottom: 16px"
    />

    <!-- Device Settings Modal -->
    <DeviceSettings v-model:visible="showDeviceSettings" />

    <!-- Participant List Drawer -->
    <ParticipantList v-model:visible="showParticipantList" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { useVideoConferenceStore } from "@/stores/videoConference";
import { storeToRefs } from "pinia";
import DeviceSettings from "./DeviceSettings.vue";
import ParticipantList from "./ParticipantList.vue";
import {
  IconSound,
  IconMute,
  IconCamera,
  IconCameraOff,
  IconDesktop,
  IconPhone,
  IconSettings,
  IconUserGroup,
} from "@arco-design/web-vue/es/icon";

// Store
const videoStore = useVideoConferenceStore();
const {
  status,
  roomId,
  displayName,
  remoteStreamsList,
  isAudioEnabled,
  isVideoEnabled,
  isScreenSharing,
  error,
  isConnected,
  isConnecting,
  hasError,
} = storeToRefs(videoStore);

// Local state
const showJoinModal = ref(true);
const showDeviceSettings = ref(false);
const showParticipantList = ref(false);
const joinForm = ref({
  displayName: "",
  roomId: "",
});

// Methods
async function handleJoinRoom() {
  if (!joinForm.value.displayName || !joinForm.value.roomId) return;

  const success = await videoStore.joinRoom(
    joinForm.value.roomId,
    joinForm.value.displayName
  );

  if (success) {
    showJoinModal.value = false;
  }
}

async function handleLeaveRoom() {
  await videoStore.leaveRoom();
  showJoinModal.value = true;
  joinForm.value = {
    displayName: "",
    roomId: "",
  };
}

async function toggleAudio() {
  await videoStore.toggleAudio();
}

async function toggleVideo() {
  await videoStore.toggleVideo();
}

async function toggleScreenShare() {
  if (isScreenSharing.value) {
    await videoStore.stopScreenSharing();
  } else {
    await videoStore.startScreenSharing();
  }
}

function clearError() {
  videoStore.clearError();
}

// Lifecycle
onMounted(async () => {
  await videoStore.initializeClient();
});

onUnmounted(async () => {
  if (isConnected.value) {
    await videoStore.leaveRoom();
  }
});
</script>

<style lang="scss" scoped>
.video-conference {
  height: 100vh;
  background: #f5f5f5;
  position: relative;
}

.join-form {
  padding: 20px 0;
}

.conference-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-grid {
  flex: 1;
  display: grid;
  gap: 16px;
  padding: 16px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-auto-rows: minmax(200px, 1fr);
  overflow-y: auto;
}

.video-item {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  min-height: 200px;

  &.local-video {
    border: 2px solid #1890ff;
  }

  &.screen-share {
    grid-column: 1 / -1;
    min-height: 400px;
  }
}

.video-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.video-element {
  width: 100%;
  height: 100%;
  background: #000;
}

.video-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.user-name {
  color: white;
  font-weight: 500;
}

.video-status {
  display: flex;
  gap: 8px;
}

.control-panel {
  background: white;
  padding: 16px;
  border-top: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.room-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

@media (max-width: 768px) {
  .video-grid {
    grid-template-columns: 1fr;
    padding: 8px;
    gap: 8px;
  }

  .control-panel {
    flex-direction: column;
    gap: 16px;
  }

  .controls {
    justify-content: center;
  }
}
</style>
