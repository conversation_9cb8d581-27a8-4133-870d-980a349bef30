import { defineStore } from "pinia";
import { ref, computed, reactive } from "vue";
import type { Client, Stream } from "@/utils/nertcUtils";
import type { MeetingConfig } from "@/config/meeting";
import { MeetingStatus, meetingConfig } from "@/config/meeting";
import * as nertcUtils from "@/utils/nertcUtils";

export const useVideoConferenceStore = defineStore("videoConference", () => {
  // State
  const client = ref<Client | null>(null);
  const localStream = ref<Stream | null>(null);
  const remoteStreams = reactive(new Map<string, Stream>());
  const status = ref<MeetingStatus>(MeetingStatus.IDLE);
  const roomId = ref<string>("");
  const displayName = ref<string>("");
  const isAudioEnabled = ref<boolean>(true);
  const isVideoEnabled = ref<boolean>(true);
  const isScreenSharing = ref<boolean>(false);
  const screenStream = ref<Stream | null>(null);
  const error = ref<string | null>(null);
  const availableDevices = reactive({
    cameras: [] as MediaDeviceInfo[],
    microphones: [] as MediaDeviceInfo[],
    speakers: [] as MediaDeviceInfo[],
  });
  const selectedDevices = reactive({
    cameraId: "",
    microphoneId: "",
    speakerId: "",
  });

  // Getters
  const isConnected = computed(() => status.value === MeetingStatus.CONNECTED);
  const isConnecting = computed(
    () => status.value === MeetingStatus.CONNECTING
  );
  const remoteStreamsList = computed(() => Array.from(remoteStreams.values()));
  const hasError = computed(() => error.value !== null);

  // Actions
  async function initializeClient(config: MeetingConfig = meetingConfig) {
    try {
      // Check browser compatibility first
      const isCompatible = await nertcUtils.checkBrowserCompatibility();
      if (!isCompatible) {
        throw new Error("Browser is not compatible with NERTC");
      }

      // Create client
      client.value = nertcUtils.createClient(config.appKey);

      // Set up event listeners
      setupClientEventListeners();

      // Get available devices
      await fetchAvailableDevices();

      return true;
    } catch (err: any) {
      error.value = err.message || "Failed to initialize client";
      return false;
    }
  }

  async function fetchAvailableDevices() {
    try {
      const devices = await nertcUtils.getAvailableDevices();
      availableDevices.cameras = devices.videos || [];
      availableDevices.microphones = devices.audios || [];
      availableDevices.speakers = devices.speakers || [];

      // Set default devices if available
      if (availableDevices.cameras.length > 0) {
        selectedDevices.cameraId = availableDevices.cameras[0].deviceId;
      }
      if (availableDevices.microphones.length > 0) {
        selectedDevices.microphoneId = availableDevices.microphones[0].deviceId;
      }
      if (availableDevices.speakers.length > 0) {
        selectedDevices.speakerId = availableDevices.speakers[0].deviceId;
      }
    } catch (err: any) {
      error.value = err.message || "Failed to fetch available devices";
    }
  }

  function setupClientEventListeners() {
    if (!client.value) return;

    nertcUtils.setupClientEventListeners(client.value, {
      onConnectionStateChange: (newState) => {
        console.log("Connection state changed:", newState);
      },
      onStreamAdded: (stream) => {
        console.log("Remote stream added:", stream.getId());
        if (client.value) {
          nertcUtils.subscribeStream(client.value, stream);
        }
      },
      onStreamSubscribed: (stream) => {
        console.log("Remote stream subscribed:", stream.getId());
        remoteStreams.set(stream.getId(), stream);

        // Play the remote stream
        stream.play(`remote-stream-${stream.getId()}`);
      },
      onStreamRemoved: (stream) => {
        console.log("Remote stream removed:", stream.getId());

        // Stop playing the stream
        stream.stop();

        // Remove from our map
        remoteStreams.delete(stream.getId());
      },
      onPeerOnline: (uid) => {
        console.log("Peer joined:", uid);
      },
      onPeerLeave: (uid) => {
        console.log("Peer left:", uid);
      },
      onError: (err) => {
        console.error("Client error:", err);
        error.value = err.msg || "An error occurred";
      },
    });
  }

  async function createLocalStream() {
    try {
      if (localStream.value) {
        await localStream.value.destroy();
        localStream.value = null;
      }

      console.log("正在创建本地流，设备配置:", {
        microphoneId: selectedDevices.microphoneId,
        cameraId: selectedDevices.cameraId,
      });

      localStream.value = await nertcUtils.createLocalStream({
        audio: true,
        video: true,
        screen: false,
        microphoneId: selectedDevices.microphoneId,
        cameraId: selectedDevices.cameraId,
      });

      console.log("本地流创建成功:", localStream.value);
      return true;
    } catch (err: any) {
      console.error("创建本地流失败:", err);
      error.value = err.message || "Failed to create local stream";
      return false;
    }
  }

  async function joinRoom(roomName: string, userName: string) {
    try {
      if (!client.value) {
        throw new Error("Client not initialized");
      }

      status.value = MeetingStatus.CONNECTING;
      displayName.value = userName;
      roomId.value = roomName;

      // Create local stream if not already created
      if (!localStream.value) {
        const success = await createLocalStream();
        if (!success) {
          throw new Error("Failed to create local stream");
        }
      }

      // Join the room
      await nertcUtils.joinRoom(client.value, {
        channelName: roomName,
        uid: userName,
        token: meetingConfig.token,
      });

      // Publish local stream
      if (localStream.value) {
        await nertcUtils.publishStream(client.value, localStream.value);

        // 注意：这里不再调用play方法，而是在UI组件中处理
        console.log("本地流已发布，等待UI组件播放");
      }

      status.value = MeetingStatus.CONNECTED;
      return true;
    } catch (err: any) {
      status.value = MeetingStatus.FAILED;
      error.value = err.message || "Failed to join room";
      return false;
    }
  }

  async function leaveRoom() {
    try {
      if (!client.value) {
        throw new Error("Client not initialized");
      }

      // Stop screen sharing if active
      if (isScreenSharing.value) {
        await stopScreenSharing();
      }

      // Unpublish and destroy local stream
      if (localStream.value) {
        if (isConnected.value) {
          await nertcUtils.unpublishStream(client.value, localStream.value);
        }
        localStream.value.stop();
        await localStream.value.destroy();
        localStream.value = null;
      }

      // Leave the channel
      if (isConnected.value) {
        await nertcUtils.leaveRoom(client.value);
      }

      // Reset state
      remoteStreams.clear();
      roomId.value = "";
      status.value = MeetingStatus.IDLE;
      error.value = null;

      return true;
    } catch (err: any) {
      error.value = err.message || "Failed to leave room";
      return false;
    }
  }

  async function toggleAudio() {
    if (!localStream.value) return;

    try {
      if (isAudioEnabled.value) {
        localStream.value.muteAudio();
      } else {
        localStream.value.unmuteAudio();
      }
      isAudioEnabled.value = !isAudioEnabled.value;
    } catch (err: any) {
      error.value = err.message || "Failed to toggle audio";
    }
  }

  async function toggleVideo() {
    if (!localStream.value) return;

    try {
      if (isVideoEnabled.value) {
        localStream.value.muteVideo();
      } else {
        localStream.value.unmuteVideo();
      }
      isVideoEnabled.value = !isVideoEnabled.value;
    } catch (err: any) {
      error.value = err.message || "Failed to toggle video";
    }
  }

  async function startScreenSharing() {
    try {
      if (!client.value || !isConnected.value) {
        throw new Error("Not connected to a room");
      }

      // Create screen sharing stream
      screenStream.value = await nertcUtils.createLocalStream({
        audio: false,
        video: false,
        screen: true,
      });

      await nertcUtils.publishStream(client.value, screenStream.value);
      isScreenSharing.value = true;
      console.log("屏幕共享流已发布，等待UI组件播放");
    } catch (err: any) {
      error.value = err.message || "Failed to start screen sharing";
    }
  }

  async function stopScreenSharing() {
    try {
      if (!screenStream.value || !client.value) return;

      await client.value.unpublish(screenStream.value);
      screenStream.value.stop();
      await screenStream.value.destroy();
      screenStream.value = null;
      isScreenSharing.value = false;
    } catch (err: any) {
      error.value = err.message || "Failed to stop screen sharing";
    }
  }

  function clearError() {
    error.value = null;
  }

  return {
    // State
    status,
    roomId,
    displayName,
    localStream,
    screenStream,
    remoteStreams,
    remoteStreamsList,
    isAudioEnabled,
    isVideoEnabled,
    isScreenSharing,
    error,
    availableDevices,
    selectedDevices,

    // Getters
    isConnected,
    isConnecting,
    hasError,

    // Actions
    initializeClient,
    createLocalStream,
    joinRoom,
    leaveRoom,
    toggleAudio,
    toggleVideo,
    startScreenSharing,
    stopScreenSharing,
    fetchAvailableDevices,
    clearError,
  };
});
