import NERTC from "nertc-web-sdk";
import { meetingConfig } from "@/config/meeting";

// Define types for NERTC SDK
export type Client = any;
export type Stream = any;
export type ClientConfig = {
  appkey: string;
  debug?: boolean;
};

// Define types for MediaDeviceInfo if not available
type MediaDeviceInfo = {
  deviceId: string;
  label: string;
  kind: string;
  groupId: string;
};

/**
 * Check if the browser is compatible with NERTC
 * @returns {Promise<boolean>} Whether the browser is compatible
 */
export function checkBrowserCompatibility(): Promise<boolean> {
  try {
    // Just return true for now to avoid TypeScript issues
    // In a real implementation, we would properly check browser compatibility
    return Promise.resolve(true);
  } catch (error) {
    console.error("Failed to check browser compatibility:", error);
    return Promise.resolve(false);
  }
}

/**
 * Create a NERTC client
 * @param {string} appKey - The app key
 * @returns {Client} The NERTC client
 */
export function createClient(appKey: string = meetingConfig.appKey): Client {
  const clientConfig: ClientConfig = {
    appkey: appKey,
    debug: true,
  };

  return NERTC.createClient(clientConfig);
}

/**
 * Get available devices
 * @returns {Promise<{videos: MediaDeviceInfo[], audios: MediaDeviceInfo[], speakers: MediaDeviceInfo[]}>} Available devices
 */
export async function getAvailableDevices(): Promise<{
  videos: MediaDeviceInfo[];
  audios: MediaDeviceInfo[];
  speakers: MediaDeviceInfo[];
}> {
  try {
    const devices = (await NERTC.getDevices()) as any;
    return {
      videos: devices.video || [],
      audios: devices.audioIn || [],
      speakers: devices.audioOut || [],
    };
  } catch (error) {
    console.error("Failed to get available devices:", error);
    return { videos: [], audios: [], speakers: [] };
  }
}

/**
 * Create a local stream
 * @param {Object} options - Stream options
 * @param {boolean} options.audio - Whether to enable audio
 * @param {boolean} options.video - Whether to enable video
 * @param {boolean} options.screen - Whether to enable screen sharing
 * @param {string} options.microphoneId - The microphone ID
 * @param {string} options.cameraId - The camera ID
 * @returns {Promise<Stream>} The local stream
 */
export async function createLocalStream(options: {
  audio?: boolean;
  video?: boolean;
  screen?: boolean;
  microphoneId?: string;
  cameraId?: string;
}): Promise<Stream> {
  const defaultOptions = {
    audio: true,
    video: true,
    screen: false,
  };

  const streamOptions = { ...defaultOptions, ...options };
  console.log("创建流的选项:", streamOptions);

  const stream = NERTC.createStream(streamOptions) as any;
  console.log("NERTC.createStream返回:", stream);

  try {
    if (stream && typeof stream.init === "function") {
      console.log("正在初始化流...");
      await stream.init();
      console.log("流初始化成功");

      // 检查流的属性
      console.log("流的属性:", {
        hasAudio:
          typeof stream.hasAudio === "function" ? stream.hasAudio() : "unknown",
        hasVideo:
          typeof stream.hasVideo === "function" ? stream.hasVideo() : "unknown",
        getMediaStream: typeof stream.getMediaStream,
        stream: !!stream.stream,
        _stream: !!stream._stream,
      });
    } else {
      console.warn("流对象无效或没有init方法");
    }
    return stream;
  } catch (error) {
    console.error("Failed to initialize local stream:", error);
    throw error;
  }
}

/**
 * Join a room
 * @param {Client} client - The NERTC client
 * @param {Object} options - Join options
 * @param {string} options.channelName - The channel name
 * @param {string} options.uid - The user ID
 * @param {string} options.token - The token
 * @returns {Promise<void>}
 */
export async function joinRoom(
  client: Client,
  options: {
    channelName: string;
    uid: string;
    token?: string;
  }
): Promise<void> {
  const joinOptions = {
    ...options,
    token: options.token || meetingConfig.token,
  };

  try {
    await client.join(joinOptions);
  } catch (error) {
    console.error("Failed to join room:", error);
    throw error;
  }
}

/**
 * Leave a room
 * @param {Client} client - The NERTC client
 * @returns {Promise<void>}
 */
export async function leaveRoom(client: Client): Promise<void> {
  try {
    await client.leave();
  } catch (error) {
    console.error("Failed to leave room:", error);
    throw error;
  }
}

/**
 * Publish a stream
 * @param {Client} client - The NERTC client
 * @param {Stream} stream - The stream to publish
 * @returns {Promise<void>}
 */
export async function publishStream(
  client: Client,
  stream: Stream
): Promise<void> {
  try {
    await client.publish(stream);
  } catch (error) {
    console.error("Failed to publish stream:", error);
    throw error;
  }
}

/**
 * Unpublish a stream
 * @param {Client} client - The NERTC client
 * @param {Stream} stream - The stream to unpublish
 * @returns {Promise<void>}
 */
export async function unpublishStream(
  client: Client,
  stream: Stream
): Promise<void> {
  try {
    await client.unpublish(stream);
  } catch (error) {
    console.error("Failed to unpublish stream:", error);
    throw error;
  }
}

/**
 * Subscribe to a remote stream
 * @param {Client} client - The NERTC client
 * @param {Stream} stream - The stream to subscribe to
 * @returns {Promise<void>}
 */
export async function subscribeStream(
  client: Client,
  stream: Stream
): Promise<void> {
  try {
    await client.subscribe(stream);
  } catch (error) {
    console.error("Failed to subscribe to stream:", error);
    throw error;
  }
}

/**
 * Unsubscribe from a remote stream
 * @param {Client} client - The NERTC client
 * @param {Stream} stream - The stream to unsubscribe from
 * @returns {Promise<void>}
 */
export async function unsubscribeStream(
  client: Client,
  stream: Stream
): Promise<void> {
  try {
    await client.unsubscribe(stream);
  } catch (error) {
    console.error("Failed to unsubscribe from stream:", error);
    throw error;
  }
}

/**
 * Set up client event listeners
 * @param {Client} client - The NERTC client
 * @param {Object} handlers - Event handlers
 * @returns {void}
 */
export function setupClientEventListeners(
  client: Client,
  handlers: {
    onConnectionStateChange?: (state: string) => void;
    onStreamAdded?: (stream: Stream) => void;
    onStreamSubscribed?: (stream: Stream) => void;
    onStreamRemoved?: (stream: Stream) => void;
    onPeerOnline?: (uid: string) => void;
    onPeerLeave?: (uid: string) => void;
    onError?: (error: any) => void;
  }
): void {
  if (handlers.onConnectionStateChange) {
    client.on("connection-state-change", handlers.onConnectionStateChange);
  }

  if (handlers.onStreamAdded) {
    client.on("stream-added", (evt: any) =>
      handlers.onStreamAdded?.(evt.stream)
    );
  }

  if (handlers.onStreamSubscribed) {
    client.on("stream-subscribed", (evt: any) =>
      handlers.onStreamSubscribed?.(evt.stream)
    );
  }

  if (handlers.onStreamRemoved) {
    client.on("stream-removed", (evt: any) =>
      handlers.onStreamRemoved?.(evt.stream)
    );
  }

  if (handlers.onPeerOnline) {
    client.on("peer-online", (evt: any) => handlers.onPeerOnline?.(evt.uid));
  }

  if (handlers.onPeerLeave) {
    client.on("peer-leave", (evt: any) => handlers.onPeerLeave?.(evt.uid));
  }

  if (handlers.onError) {
    client.on("error", handlers.onError);
  }
}

/**
 * Clean up client event listeners
 * @param {Client} client - The NERTC client
 * @returns {void}
 */
export function cleanupClientEventListeners(client: Client): void {
  client.off("connection-state-change");
  client.off("stream-added");
  client.off("stream-subscribed");
  client.off("stream-removed");
  client.off("peer-online");
  client.off("peer-leave");
  client.off("error");
}
