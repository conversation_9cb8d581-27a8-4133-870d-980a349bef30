/**
 * 生成随机的会议房间ID
 * @param {number} length - 房间ID长度
 * @returns {string} 生成的房间ID
 */
export function generateRoomId(length: number = 9): string {
  // 生成数字房间ID
  const digits = '0123456789';
  let result = '';
  
  // 确保第一位不是0
  result += digits.charAt(Math.floor(Math.random() * 9) + 1);
  
  // 生成剩余位数
  for (let i = 1; i < length; i++) {
    result += digits.charAt(Math.floor(Math.random() * digits.length));
  }
  
  // 每3位插入一个分隔符，便于阅读
  return result.replace(/(\d{3})(?=\d)/g, '$1-');
}

/**
 * 解析URL中的房间参数
 * @returns {Object} 包含房间ID和密码的对象
 */
export function parseRoomParams(): { roomId: string; password: string } {
  const urlParams = new URLSearchParams(window.location.search);
  return {
    roomId: urlParams.get('room') || '',
    password: urlParams.get('password') || '',
  };
}

/**
 * 验证房间ID格式是否有效
 * @param {string} roomId - 要验证的房间ID
 * @returns {boolean} 是否有效
 */
export function validateRoomId(roomId: string): boolean {
  // 移除可能的分隔符
  const cleanId = roomId.replace(/-/g, '');
  
  // 检查是否为纯数字且长度合适
  return /^\d{6,12}$/.test(cleanId);
}

/**
 * 格式化房间ID，添加分隔符
 * @param {string} roomId - 要格式化的房间ID
 * @returns {string} 格式化后的房间ID
 */
export function formatRoomId(roomId: string): string {
  // 移除现有分隔符
  const cleanId = roomId.replace(/-/g, '');
  
  // 每3位添加分隔符
  return cleanId.replace(/(\d{3})(?=\d)/g, '$1-');
}

/**
 * 生成随机的会议密码
 * @param {number} length - 密码长度
 * @returns {string} 生成的密码
 */
export function generateRoomPassword(length: number = 6): string {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}
