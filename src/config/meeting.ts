// NEMeeting 配置文件
export interface MeetingConfig {
  appKey: string;
  serverUrl: string;
  userUuid: string;
  token: string;
  password: string;
}

// 默认配置 - 请根据您的实际情况修改
export const meetingConfig: MeetingConfig = {
  // 请在网易云信控制台获取您的AppKey
  appKey: "febb2cfb7ca924ab324f30dce5d00f2a",

  // 服务器地址，通常使用默认值
  serverUrl: "https://meeting.yunxinroom.com",

  // 用户UUID - 请填入您的用户标识
  userUuid: "meeting",

  // Token - 用于Token登录方式
  token: "meeting",

  // 密码 - 用于账号密码登录方式
  password: "Sqray123457",
};

// 会议状态枚举
export enum MeetingStatus {
  IDLE = "IDLE",
  CONNECTING = "CONNECTING",
  CONNECTED = "CONNECTED",
  DISCONNECTED = "DISCONNECTED",
  FAILED = "FAILED",
}

// 会议参数接口
export interface MeetingParams {
  displayName: string;
  meetingNum?: string;
}

// 初始化参数接口
export interface InitParams {
  appKey: string;
  serverUrl: string;
  width: number;
  height: number;
}
